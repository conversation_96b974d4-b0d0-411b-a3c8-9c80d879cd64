"use client";
import { useEffect, useState, useRef, useCallback } from "react";
import { whisperTranscribe } from "@/lib/groq";
import { saveInterview, type ChatMessage } from "@/utils/interviewStorage";
import { useRouter, useSearchParams } from "next/navigation";
import InterviewAvatar, {
  type InterviewAvatarRef,
} from "@/components/dashboard/interview/InterviewAvatar";
import { BrainCircuit, AlertTriangle, Mic, CheckCircle } from "lucide-react";
import { getNextAvatarMessage } from "@/lib/interviewLlm";
import { Button } from "@/components/ui/button";

type InterviewState =
  | "initializing"
  | "avatar_speaking"
  | "waiting_for_answer"
  | "recording"
  | "processing"
  | "completed"
  | "error";

function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
}

const FILLER_WORDS = new Set([
  "uh",
  "um",
  "umm",
  "ah",
  "ahh",
  "like",
  "you know",
  "so",
  "actually",
  "basically",
  "literally",
]);

function countFillerWords(text: string): number {
  if (!text) return 0;
  const words = text.toLowerCase().split(/[\s,.]+/);
  let count = 0;
  for (const word of words) {
    if (FILLER_WORDS.has(word)) {
      count++;
    }
  }
  return count;
}

// Convert database questions to the format expected by getNextAvatarMessage
function convertQuestionsToFormat(questions: any[], category: string) {
  const questionsByCategory: {
    [key: string]: Array<{ id: string; text: string }>;
  } = {
    communication: [],
    technical: [],
    behavioral: [],
  };

  // Add all questions to the selected category
  questions.forEach((q) => {
    if (q.category === category && questionsByCategory[category]) {
      questionsByCategory[category].push({
        id: q.id,
        text: q.questionText,
      });
    }
  });

  // If no questions found for the category, log for debugging
  if (questionsByCategory[category].length === 0) {
    console.warn(`No questions found for category: ${category}`);
    console.log("Available questions:", questions);
  }

  return questionsByCategory;
}

export default function InterviewLivePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [interviewState, setInterviewState] =
    useState<InterviewState>("initializing");

  const [chat, setChat] = useState<ChatMessage[]>([]);
  const interviewIdRef = useRef<string>(Date.now().toString());
  const sessionIdRef = useRef<string>("");
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<BlobPart[]>([]);
  const [avatarText, setAvatarText] = useState<string>("");
  const [timer, setTimer] = useState(0);
  const [interviewTimer, setInterviewTimer] = useState(0); // Overall interview timer
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const interviewTimerRef = useRef<NodeJS.Timeout | null>(null);
  const avatarRef = useRef<InterviewAvatarRef>(null);
  const questionFinishTimeRef = useRef<number | null>(null);
  const userMetricsRef = useRef({ responseTime: 0, answerDuration: 0 });
  const [questions, setQuestions] = useState<any[]>([]);
  const [category, setCategory] = useState<string>("");
  const [firm, setFirm] = useState<string>("");
  const [interviewDuration] = useState(30); // 2 minutes in seconds
  const [avatarSpeaking, setAvatarSpeaking] = useState(false);
  const timerRef = useRef(0);
  const [interviewStarted, setInterviewStarted] = useState(false);

  const handleUserAnswer = useCallback(
    async (answerText: string) => {
      try {
        const fillerWordCount = countFillerWords(answerText);
        const { responseTime, answerDuration } = userMetricsRef.current;
        const userMessage: ChatMessage = {
          from: "user",
          message: answerText,
          timestamp: Date.now(),
          responseTime,
          answerDuration,
          fillerWordCount,
        };

        const currentChat = [...chat, userMessage];

        // Check if time limit reached BEFORE generating next question
        if (interviewTimer >= interviewDuration) {
          // End interview due to time limit
          const endMessage: ChatMessage = {
            from: "avatar",
            message:
              "Thank you for that answer. Our time is up for today's interview. You did great! We'll review your responses and be in touch soon. Have a wonderful day!",
            timestamp: Date.now(),
            type: "end",
            category: null,
          };

          const finalChat = [...currentChat, endMessage];
          setChat(finalChat);
          setAvatarText(endMessage.message);
          saveInterview(interviewIdRef.current, finalChat);

          setInterviewState("avatar_speaking");
          setAvatarSpeaking(true);
          setTimeout(() => {
            setAvatarSpeaking(false);
            setInterviewState("completed");
          }, 6000);
          return;
        }

        const formattedQuestions = convertQuestionsToFormat(
          questions,
          category
        );
        const avatarMessage = await getNextAvatarMessage(
          currentChat,
          formattedQuestions as any,
          1,
          true // time-based interview
        );

        const newChat = [...currentChat, avatarMessage];
        setChat(newChat);
        setAvatarText(avatarMessage.message);
        saveInterview(interviewIdRef.current, newChat);

        // Set state to indicate avatar is about to speak
        setInterviewState("avatar_speaking");

        // Only check for natural end, not time-based end (that's handled above)
        if (avatarMessage.type === "end") {
          setAvatarSpeaking(true);
          setTimeout(() => {
            setAvatarSpeaking(false);
            setInterviewState("completed");
          }, 6000);
        }
      } catch (error) {
        console.error("Error in handleUserAnswer:", error);
        setInterviewState("error");
      }
    },
    [chat, interviewTimer, interviewDuration, questions, category]
  );

  const startRecording = useCallback(async () => {
    if (interviewState !== "waiting_for_answer") return;
    try {
      const responseTime = questionFinishTimeRef.current
        ? (Date.now() - questionFinishTimeRef.current) / 1000
        : 0;
      userMetricsRef.current.responseTime = responseTime;

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mr = new MediaRecorder(stream);
      chunksRef.current = [];

      mr.ondataavailable = (e) => chunksRef.current.push(e.data);
      mr.onstop = async () => {
        const blob = new Blob(chunksRef.current, { type: "audio/webm" });
        setInterviewState("processing");
        const text = await whisperTranscribe(blob);
        handleUserAnswer(text);
      };

      mediaRecorderRef.current = mr;
      mr.start();
      setInterviewState("recording");
    } catch (err) {
      console.error("Error getting audio stream:", err);
      setInterviewState("error");
    }
  }, [interviewState, handleUserAnswer]);

  const stopRecording = useCallback(() => {
    if (interviewState !== "recording") return;
    userMetricsRef.current.answerDuration = timerRef.current;
    mediaRecorderRef.current?.stop();
    mediaRecorderRef.current?.stream
      ?.getTracks()
      .forEach((track) => track.stop());
  }, [interviewState]);

  const handleAvatarStartedSpeaking = useCallback(() => {
    setAvatarSpeaking(true);
    setInterviewState("avatar_speaking");
    // Start the interview timer when avatar first starts speaking
    if (!interviewStarted) {
      setInterviewStarted(true);
      if (!interviewTimerRef.current) {
        interviewTimerRef.current = setInterval(() => {
          setInterviewTimer((prev) => prev + 1);
        }, 1000);
      }
    }
  }, [interviewStarted]);

  const handleAvatarFinished = useCallback(() => {
    setAvatarSpeaking(false);
    // Use a ref to get the current chat state to avoid stale closure issues
    setChat((currentChat) => {
      const lastMessage = currentChat[currentChat.length - 1];
      if (lastMessage?.type === "end") {
        setInterviewState("completed");
        return currentChat;
      }
      if (lastMessage?.from === "avatar" && lastMessage?.type === "question") {
        questionFinishTimeRef.current = Date.now();
        setInterviewState("waiting_for_answer");
      }
      return currentChat;
    });
  }, []);

  // Recording timer (for individual answers)
  useEffect(() => {
    if (interviewState === "recording") {
      setTimer(0);
      timerRef.current = 0;
      timerIntervalRef.current = setInterval(() => {
        setTimer((prev) => prev + 1);
        timerRef.current += 1;
      }, 1000);
    } else {
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }
    return () => {
      if (timerIntervalRef.current) clearInterval(timerIntervalRef.current);
    };
  }, [interviewState]);

  // Overall interview timer cleanup
  useEffect(() => {
    if (interviewState === "completed" || interviewState === "error") {
      // Stop timer when interview ends
      if (interviewTimerRef.current) {
        clearInterval(interviewTimerRef.current);
        interviewTimerRef.current = null;
      }
    }

    return () => {
      if (interviewTimerRef.current) {
        clearInterval(interviewTimerRef.current);
        interviewTimerRef.current = null;
      }
    };
  }, [interviewState]);

  useEffect(() => {
    const initializeInterview = async () => {
      if (chat.length > 0) return;

      try {
        // Get parameters from URL
        const categoryParam = searchParams.get("category");
        const firmParam = searchParams.get("firm");

        if (!categoryParam || !firmParam) {
          router.push("/dashboard/interview/select");
          return;
        }

        setCategory(categoryParam);
        setFirm(firmParam);

        // Create interview session
        const sessionResponse = await fetch("/api/interview/session", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            category: categoryParam,
            firmName: firmParam,
            durationMinutes: 2,
          }),
        });

        if (!sessionResponse.ok) {
          throw new Error("Failed to create interview session");
        }

        const sessionData = await sessionResponse.json();
        sessionIdRef.current = sessionData.sessionId;

        // Fetch questions
        const questionsResponse = await fetch(
          `/api/questions?category=${categoryParam}&firm=${firmParam}`
        );

        if (!questionsResponse.ok) {
          throw new Error("Failed to fetch questions");
        }

        const questionsData = await questionsResponse.json();
        setQuestions(questionsData.questions);

        // Start interview with first message
        const formattedQuestions = convertQuestionsToFormat(
          questionsData.questions,
          categoryParam
        );
        const firstMsg = await getNextAvatarMessage(
          [],
          formattedQuestions as any,
          1,
          true // time-based interview
        );
        setChat([firstMsg]);
        setAvatarText(firstMsg.message);
        // Set state to indicate avatar is about to speak
        setInterviewState("avatar_speaking");
      } catch (error) {
        console.error("Error in initial message fetch:", error);
        setInterviewState("error");
      }
    };

    const completeInterview = async () => {
      // Stop all timers first
      if (interviewTimerRef.current) {
        clearInterval(interviewTimerRef.current);
        interviewTimerRef.current = null;
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }

      // Wait a moment for avatar to finish if speaking
      if (avatarRef.current) {
        await new Promise((resolve) => setTimeout(resolve, 2000)); // 2 second delay
        await avatarRef.current.stopSession();
      }

      try {
        // Generate and save report to database
        const { generateInterviewReport } = await import("@/lib/interviewLlm");
        const report = await generateInterviewReport(chat);

        // Save report to database

        const reportResponse = await fetch("/api/interview/report", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sessionId: sessionIdRef.current,
            overallScore: report.overallScore,
            oneLineSummary: report.oneLineSummary,
            strengths: report.strengths,
            areasForImprovement: report.areasForImprovement,
            averageResponseTime: report.averageResponseTime,
            averageAnswerDuration: report.averageAnswerDuration,
            totalFillerWords: report.totalFillerWords,
            questionAnalysis: report.questionAnalysis,
          }),
        });

        if (!reportResponse.ok) {
          console.error("Failed to save report to database");
        }

        // Still save to localStorage as backup
        saveInterview(interviewIdRef.current, chat);
      } catch (error) {
        console.error("Error generating/saving report:", error);
        // Fallback to localStorage
        saveInterview(interviewIdRef.current, chat);
      }

      // Add a final delay before navigation
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.replace(`/dashboard/interview/report/${sessionIdRef.current}`);
    };

    if (interviewState === "initializing") {
      initializeInterview();
    }

    if (interviewState === "completed") {
      completeInterview();
    }
  }, [interviewState, router, chat.length, searchParams]); // eslint-disable-line react-hooks/exhaustive-deps

  if (interviewState === "completed") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-2xl p-8 shadow-lg border max-w-sm mx-auto text-center">
          <div className="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Interview Complete!
          </h2>
          <p className="text-gray-600">
            {avatarSpeaking ? "Finishing up..." : "Generating your report..."}
          </p>
          <div className="mt-6 flex justify-center">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <div
                className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"
                style={{ animationDelay: "0.2s" }}
              ></div>
              <div
                className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"
                style={{ animationDelay: "0.4s" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-6 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Mock Interview</h1>
          </div>

          <div className="flex items-center gap-3">
            {/* Interview Timer */}
            <div className="bg-white px-4 py-2 rounded-lg border shadow-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span className="text-sm font-medium text-gray-700">
                  {formatTime(interviewTimer)} / {formatTime(interviewDuration)}
                </span>
              </div>
            </div>

            {/* Status */}
            <div className="bg-white px-4 py-2 rounded-lg border shadow-sm">
              <div className="flex items-center space-x-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    interviewState === "recording"
                      ? "bg-red-500 animate-pulse"
                      : interviewState === "processing"
                      ? "bg-yellow-500 animate-pulse"
                      : interviewState === "avatar_speaking"
                      ? "bg-blue-500 animate-pulse"
                      : "bg-emerald-500"
                  }`}
                ></div>
                <span className="text-sm font-medium text-gray-700 capitalize">
                  {interviewState === "waiting_for_answer"
                    ? "Ready"
                    : interviewState === "avatar_speaking"
                    ? "Speaking"
                    : interviewState.replace("_", " ")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-sm border">
          {/* Avatar Section */}
          <div className="p-6 border-b">
            <div className="max-w-4xl mx-auto">
              <InterviewAvatar
                ref={avatarRef}
                textToSpeak={avatarText}
                onFinishedSpeaking={handleAvatarFinished}
                onStartedSpeaking={handleAvatarStartedSpeaking}
              />
            </div>
          </div>

          {/* Controls Section */}
          <div className="p-6">
            <div className="max-w-md mx-auto">
              {interviewState === "recording" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mx-auto relative">
                    <div className="absolute inset-0 rounded-full bg-red-500 opacity-30 animate-ping"></div>
                    <Mic className="w-4 h-4 text-gray-800 relative z-10" />
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Recording
                    </h3>
                    <div className="text-2xl font-mono font-bold text-gray-900">
                      {formatTime(timer)}
                    </div>
                  </div>
                  <Button onClick={stopRecording}>Finish Answer</Button>
                </div>
              )}

              {interviewState === "waiting_for_answer" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mx-auto">
                    <Mic className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Ready to record
                    </h3>
                    <p className="text-gray-600">
                      Click to start recording your answer
                    </p>
                  </div>
                  <Button onClick={startRecording}>Start Recording</Button>
                </div>
              )}

              {interviewState === "processing" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mx-auto relative">
                    <BrainCircuit className="w-4 h-4 text-white" />
                    <div className="absolute inset-0 rounded-full border-2 border-emerald-300 animate-spin border-t-transparent"></div>
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Processing
                    </h3>
                    <p className="text-gray-600">Analyzing your response...</p>
                  </div>
                </div>
              )}

              {interviewState === "error" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                    <AlertTriangle className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Error
                    </h3>
                    <p className="text-gray-600">
                      Please check your microphone and try again
                    </p>
                  </div>
                  <Button onClick={() => window.location.reload()}>
                    Refresh Page
                  </Button>
                </div>
              )}

              {interviewState === "initializing" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mx-auto relative">
                    <BrainCircuit className="w-8 h-8 text-white" />
                    <div className="absolute inset-0 rounded-full border-2 border-emerald-300 animate-spin border-t-transparent"></div>
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Initializing
                    </h3>
                    <p className="text-gray-600">Preparing your interview...</p>
                  </div>
                </div>
              )}

              {interviewState === "avatar_speaking" && (
                <div className="text-center space-y-6">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
                    <BrainCircuit className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-md font-semibold text-gray-900 mb-1">
                      Interviewer Speaking
                    </h3>
                    <p className="text-gray-600">Please listen carefully...</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
