"use server";

import { type ChatCompletionMessageParam } from "@/lib/groq";
import Groq from "groq-sdk";
import { type ChatMessage } from "@/utils/interviewStorage";
import {
  interviewQuestions,
  CATEGORIES,
  QUESTIONS_PER_CATEGORY,
} from "./interviewQuestions";

type InterviewCategory = "communication" | "technical" | "behavioral";
type QuestionObject = { id: string; text: string };
type QuestionsByCategory = Record<InterviewCategory, QuestionObject[]>;

async function llamaChatInterview(
  messages: ChatCompletionMessageParam[]
): Promise<any> {
  const apiKey = process.env.GROQ_API_KEY || "";
  if (!apiKey) {
    console.warn("GROQ_API_KEY is not set");
    throw new Error("Groq API key not configured");
  }

  const groq = new Groq({ apiKey });

  try {
    const response = await groq.chat.completions.create({
      model: "meta-llama/llama-4-maverick-17b-128e-instruct",
      messages: messages as any[],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No content in response from AI");
    }

    try {
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/);
      const jsonContent = jsonMatch ? jsonMatch[1] : content;
      const parsed = JSON.parse(jsonContent);
      console.log("LLM Response:", parsed);
      return parsed;
    } catch (e) {
      console.error("Failed to parse response as JSON. Content was:", content);
      return {
        message: "Can you tell me about your experience in this area?",
        type: "question",
        category: "behavioral",
        error: "Could not parse AI response properly",
      };
    }
  } catch (error) {
    console.error("Error in llamaChatInterview:", error);
    return {
      message: "What can you tell me about your background in this field?",
      type: "question",
      category: "behavioral",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

function analyzeInterviewProgress(
  chatHistory: ChatMessage[],
  questionsPerCategory: number = 1
) {
  const categoryProgress: Record<string, number> = {};
  const askedQuestions = new Set<string>();

  CATEGORIES.forEach((cat) => {
    categoryProgress[cat] = 0;
  });

  for (const message of chatHistory) {
    if (
      message.from === "avatar" &&
      message.type === "question" &&
      message.category
    ) {
      if (CATEGORIES.includes(message.category)) {
        if (!askedQuestions.has(message.message)) {
          categoryProgress[message.category]++;
        }
      }
      askedQuestions.add(message.message);
    }
  }

  const totalQuestionsAsked = askedQuestions.size;
  const totalQuestionsNeeded = CATEGORIES.length * questionsPerCategory;

  const allCategoriesComplete = CATEGORIES.every(
    (cat) => categoryProgress[cat] >= questionsPerCategory
  );

  const shouldEnd =
    allCategoriesComplete || totalQuestionsAsked >= totalQuestionsNeeded;

  console.log("Interview Progress Analysis:", {
    categoryProgress,
    totalQuestionsAsked,
    totalQuestionsNeeded,
    allCategoriesComplete,
    shouldEnd,
  });

  return {
    categoryProgress,
    askedQuestions,
    totalQuestionsAsked,
    totalQuestionsNeeded,
    pendingCategories: CATEGORIES.filter(
      (cat) => categoryProgress[cat] < questionsPerCategory
    ),
    shouldEnd,
  };
}

function buildDynamicInterviewPrompt(
  availableQuestions: QuestionsByCategory,
  chatHistory: ChatMessage[],
  questionsPerCategory: number = 1,
  timeBasedInterview: boolean = true
): string {
  // Always analyze progress to track asked questions, even for time-based interviews
  const baseAnalysis = analyzeInterviewProgress(
    chatHistory,
    questionsPerCategory
  );

  const analysis = timeBasedInterview
    ? {
        ...baseAnalysis,
        shouldEnd: false, // Override shouldEnd for time-based interviews
        pendingCategories: Object.keys(
          availableQuestions
        ) as InterviewCategory[],
        totalQuestionsNeeded: 999, // High number for time-based
      }
    : baseAnalysis;

  const isFirstQuestion = chatHistory.length === 0;
  const lastUserMessage = chatHistory.findLast(
    (m) => m.from === "user"
  )?.message;

  return `You are Shawn, a professional Big 4 interviewer conducting a mock interview. Your goal is to create a natural, conversational interview experience.

INTERVIEW STATE:
- Questions asked: ${analysis.totalQuestionsAsked} / ${
    analysis.totalQuestionsNeeded
  }
- Category Progress: ${JSON.stringify(analysis.categoryProgress)}
- PENDING CATEGORIES: [${analysis.pendingCategories.join(", ")}]
- Should End Interview: ${analysis.shouldEnd}

AVAILABLE QUESTIONS (unasked only):
${JSON.stringify(
  Object.entries(availableQuestions).reduce((acc, [category, questions]) => {
    // Filter out already asked questions from all categories
    const unaskedQuestions = questions.filter(
      (q) => !analysis.askedQuestions.has(q.text)
    );

    // Only include categories that have unasked questions and are pending
    if (
      analysis.pendingCategories.includes(category as InterviewCategory) &&
      unaskedQuestions.length > 0
    ) {
      acc[category as InterviewCategory] = unaskedQuestions.map((q) => q.text);
    }
    return acc;
  }, {} as Record<string, string[]>),
  null,
  2
)}

CONVERSATION CONTEXT:
${
  isFirstQuestion
    ? "This is the start of the interview."
    : `The candidate just said: "${lastUserMessage}"`
}

YOUR TASK:
${
  analysis.shouldEnd
    ? `The interview MUST end now. You have asked enough questions.
 Respond with type "end" and provide a professional closing message thanking the candidate.`
    : `Continue the interview:
1. ${
        isFirstQuestion
          ? "Start with a warm welcome."
          : "Acknowledge the user's response naturally (1-2 sentences)."
      }
2. Ask ONE new question from one of the PENDING CATEGORIES.
3. Make the transition smooth and conversational.`
}

RESPONSE FORMAT (JSON only, no other text):
{
 "message": "${
   analysis.shouldEnd
     ? "Professional closing message."
     : "Your acknowledgment + chosen question."
 }",
 "type": "${analysis.shouldEnd ? "end" : "question"}",
 "category": ${analysis.shouldEnd ? "null" : '"category_of_chosen_question"'}
}

CRITICAL RULES:
- If shouldEnd is true, you MUST respond with type "end".
- ONLY ask questions from the "AVAILABLE QUESTIONS" list above.
- NEVER repeat a question that has already been asked.
- Choose questions ONLY from categories listed in "PENDING CATEGORIES".
- If no questions are available for pending categories, end the interview.
- Your entire response MUST be a single, valid JSON object.`;
}

export async function getNextAvatarMessage(
  chatHistory: ChatMessage[],
  availableQuestions: QuestionsByCategory,
  questionsPerCategory: number = 1,
  timeBasedInterview: boolean = true
): Promise<ChatMessage> {
  try {
    // For time-based interviews, we don't end based on question count
    // The calling code will handle time limits
    if (!timeBasedInterview) {
      const analysis = analyzeInterviewProgress(
        chatHistory,
        questionsPerCategory
      );
      if (analysis.shouldEnd) {
        console.log(
          "Analysis concluded interview should end. Forcing termination."
        );
        return {
          from: "avatar",
          message:
            "Thank you for taking the time to speak with me today. That concludes our interview. We'll review your responses and be in touch soon. Have a great day!",
          timestamp: Date.now(),
          type: "end",
          category: null,
        };
      }
    }

    const systemPrompt = buildDynamicInterviewPrompt(
      availableQuestions,
      chatHistory,
      questionsPerCategory,
      timeBasedInterview
    );

    const llmMessages: ChatCompletionMessageParam[] = [
      { role: "system", content: systemPrompt },
      {
        role: "user",
        content:
          chatHistory.length === 0
            ? "Please start the interview."
            : `My last response was: "${
                chatHistory[chatHistory.length - 1].message
              }". Please continue the interview.`,
      },
    ];

    const resp = await llamaChatInterview(llmMessages);

    if (resp.type === "end") {
      console.log("LLM requested to end the interview.");
      return {
        from: "avatar",
        message:
          resp.message || "Thank you for your time. The interview is complete.",
        timestamp: Date.now(),
        type: "end",
        category: null,
      };
    }

    // Find the question ID by matching the response message with available questions
    let questionId: string | undefined;
    for (const [, questions] of Object.entries(availableQuestions)) {
      const matchingQuestion = questions.find(
        (q) => q.text === resp.message || resp.message.includes(q.text)
      );
      if (matchingQuestion) {
        questionId = matchingQuestion.id;
        console.log(
          `Found matching question ID: ${questionId} for message: "${resp.message}"`
        );
        break;
      }
    }

    if (!questionId) {
      console.warn(`No question ID found for LLM response: "${resp.message}"`);
    }

    return {
      from: "avatar",
      message: resp.message,
      timestamp: Date.now(),
      type: "question",
      category: resp.category || null,
      questionId,
    };
  } catch (error) {
    console.error(
      "Error in getNextAvatarMessage, using fallback logic:",
      error
    );
    const analysis = analyzeInterviewProgress(
      chatHistory,
      questionsPerCategory
    );

    if (analysis.shouldEnd || analysis.pendingCategories.length === 0) {
      return {
        from: "avatar",
        message: "That concludes our interview. Thank you for your time.",
        timestamp: Date.now(),
        type: "end",
        category: null,
      };
    }

    const nextCategory = analysis.pendingCategories[0];
    const questionsForCategory = (
      availableQuestions[nextCategory] || []
    ).filter((q) => !analysis.askedQuestions.has(q.text));
    const selectedQuestion = questionsForCategory[0];
    const question =
      selectedQuestion?.text ||
      `Let's move to ${nextCategory}. Can you tell me about your experience here?`;

    return {
      from: "avatar",
      message: question,
      timestamp: Date.now(),
      type: "question",
      category: nextCategory,
      questionId: selectedQuestion?.id,
    };
  }
}

export interface InterviewReport {
  overallScore: number;
  oneLineSummary: string;
  strengths: string[];
  areasForImprovement: string[];
  averageResponseTime: number;
  averageAnswerDuration: number;
  totalFillerWords: number;
  questionAnalysis: Array<{
    question: string;
    category: string;
    answer: string;
    score: number;
    feedback: string;
    improvements: string[];
    responseTime?: number;
    answerDuration?: number;
    fillerWordCount?: number;
  }>;
}

function buildEnhancedReportPrompt(chatHistory: ChatMessage[]): string {
  const questionAnswerPairs = [];
  for (let i = 0; i < chatHistory.length - 1; i++) {
    const current = chatHistory[i];
    const next = chatHistory[i + 1];

    if (
      current.from === "avatar" &&
      current.type === "question" &&
      next.from === "user"
    ) {
      questionAnswerPairs.push({
        question: current.message,
        category: current.category || "general",
        answer: {
          text: next.message,
          responseTime: next.responseTime,
          answerDuration: next.answerDuration,
          fillerWordCount: next.fillerWordCount,
        },
      });
    }
  }

  return `You are a helpful career coach providing feedback on a mock interview. Your goal is to give constructive, actionable advice based on the user's actual responses. Avoid generic advice.

TRANSCRIPT (with user answer metrics):
${JSON.stringify(questionAnswerPairs, null, 2)}

Based on the transcript, provide a detailed report. Analyze the user's pacing (response time, answer duration) and clarity (filler words). Provide actionable feedback on these metrics. For example, if response time is high, suggest they practice thinking on their feet. If filler words are numerous, suggest they pause instead.

RESPONSE FORMAT (JSON only, no other text):
{
  "overallScore": <number, 1-10, average of question scores>,
  "oneLineSummary": "<string, a concise one-line summary of the performance>",
  "strengths": [
    "<string, a key strength observed in the user's answers>",
    "<string, another key strength>"
  ],
  "areasForImprovement": [
    "<string, a key area for improvement based on the answers>",
    "<string, another area for improvement>"
  ],
  "questionAnalysis": [
    {
      "question": "<string, the question that was asked>",
      "category": "<string, the category of the question>",
      "answer": "<string, the user's answer>",
      "score": <number, 1-10>,
      "feedback": "<string, detailed, constructive feedback on this specific answer, what could be imrpoved (focus on this), also including comments on pacing and filler words>",
      "improvements": [
        "<string, a specific suggestion to improve this answer>"
      ]
    }
  ]
}

SCORING GUIDELINES:
- 9-10: Exceptional answer. Clear, concise, and compelling.
- 7-8: Good answer. Solid content with minor room for refinement.
- 5-6: Average answer. Addresses the question but lacks depth or clarity.
- 3-4: Below average. Significant parts of the answer are unclear or weak.
- 1-2: Poor answer. Fails to address the core of the question.

REQUIREMENTS:
- Base all feedback directly on the provided answers and metrics.
- Be encouraging but honest.
- Focus on actionable advice.
- Must return valid JSON only.`;
}

export async function generateInterviewReport(
  chatHistory: ChatMessage[]
): Promise<InterviewReport> {
  const prompt = buildEnhancedReportPrompt(chatHistory);
  const llmMessages: ChatCompletionMessageParam[] = [
    { role: "system", content: prompt },
    {
      role: "user",
      content:
        "Please analyze this interview session and generate a comprehensive performance report with scores and actionable feedback.",
    },
  ];

  try {
    console.log("Generating enhanced interview report...");
    const resp = await llamaChatInterview(llmMessages);

    console.log("Raw report response:", JSON.stringify(resp, null, 2));

    const userAnswers = chatHistory.filter(
      (m) => m.from === "user" && m.message
    );
    const totalFillerWords = userAnswers.reduce(
      (sum, m) => sum + (m.fillerWordCount || 0),
      0
    );
    const totalResponseTime = userAnswers.reduce(
      (sum, m) => sum + (m.responseTime || 0),
      0
    );
    const totalAnswerDuration = userAnswers.reduce(
      (sum, m) => sum + (m.answerDuration || 0),
      0
    );

    const averageResponseTime =
      userAnswers.length > 0 ? totalResponseTime / userAnswers.length : 0;
    const averageAnswerDuration =
      userAnswers.length > 0 ? totalAnswerDuration / userAnswers.length : 0;

    const processedReport: InterviewReport = {
      overallScore:
        typeof resp.overallScore === "number"
          ? Math.min(10, Math.max(1, Math.round(resp.overallScore)))
          : calculateFallbackScore(chatHistory),
      oneLineSummary:
        typeof resp.oneLineSummary === "string"
          ? resp.oneLineSummary
          : "Interview completed successfully with mixed performance across different areas.",
      strengths: Array.isArray(resp.strengths)
        ? resp.strengths.slice(0, 5)
        : [
            "Completed the interview process successfully",
            "Demonstrated engagement throughout the session",
            "Showed willingness to participate and respond to questions",
          ],
      areasForImprovement: Array.isArray(resp.areasForImprovement)
        ? resp.areasForImprovement.slice(0, 5)
        : [
            "Provide more specific examples from your experience",
            "Improve answer structure and organization",
            "Practice speaking with more confidence",
          ],
      averageResponseTime: parseFloat(averageResponseTime.toFixed(2)),
      averageAnswerDuration: parseFloat(averageAnswerDuration.toFixed(2)),
      totalFillerWords,
      questionAnalysis: processQuestionAnalysis(
        resp.questionAnalysis,
        chatHistory
      ),
    };

    console.log(
      "Final processed report:",
      JSON.stringify(processedReport, null, 2)
    );
    return processedReport;
  } catch (error) {
    console.error("Error generating interview report:", error);
    return generateFallbackReport(chatHistory);
  }
}

function processQuestionAnalysis(
  analysisData: any,
  chatHistory: ChatMessage[]
): InterviewReport["questionAnalysis"] {
  if (Array.isArray(analysisData) && analysisData.length > 0) {
    return analysisData.map((qa: any) => {
      const originalMessage = chatHistory.find(
        (m) => m.from === "user" && m.message === qa.answer
      );
      return {
        question:
          typeof qa.question === "string"
            ? qa.question
            : "Question not available",
        category: typeof qa.category === "string" ? qa.category : "general",
        answer:
          typeof qa.answer === "string" ? qa.answer : "Answer not recorded",
        score:
          typeof qa.score === "number"
            ? Math.min(10, Math.max(1, qa.score))
            : 6,
        feedback:
          typeof qa.feedback === "string"
            ? qa.feedback
            : "No specific feedback available",
        improvements: Array.isArray(qa.improvements)
          ? qa.improvements.slice(0, 2)
          : [
              "Consider providing more specific examples",
              "Structure your response more clearly",
            ],
        responseTime: originalMessage?.responseTime,
        answerDuration: originalMessage?.answerDuration,
        fillerWordCount: originalMessage?.fillerWordCount,
      };
    });
  }

  const questionAnswerPairs = [];
  for (let i = 0; i < chatHistory.length - 1; i++) {
    const current = chatHistory[i];
    const next = chatHistory[i + 1];

    if (
      current.from === "avatar" &&
      current.type === "question" &&
      next.from === "user"
    ) {
      questionAnswerPairs.push({
        questionId: current.questionId, // Include the database question ID
        question: current.message,
        category: current.category || "general",
        answer: next.message,
        score: Math.floor(Math.random() * 3) + 6, // 6-8 range
        feedback:
          "Your response addressed the question. Consider adding more specific details and examples.",
        improvements: [
          "Provide more concrete examples",
          "Structure your answer more clearly",
        ],
        responseTime: next.responseTime,
        answerDuration: next.answerDuration,
        fillerWordCount: next.fillerWordCount,
      });
    }
  }

  return questionAnswerPairs;
}

function calculateFallbackScore(chatHistory: ChatMessage[]): number {
  const userResponses = chatHistory.filter((m) => m.from === "user");
  if (userResponses.length === 0) return 5;

  const avgLength =
    userResponses.reduce((sum, msg) => sum + msg.message.length, 0) /
    userResponses.length;

  if (avgLength > 200) return 8;
  if (avgLength > 100) return 7;
  if (avgLength > 50) return 6;
  return 5;
}

function generateFallbackReport(chatHistory: ChatMessage[]): InterviewReport {
  const questionAnswerPairs = [];
  const userAnswers = chatHistory.filter((m) => m.from === "user" && m.message);

  for (let i = 0; i < chatHistory.length - 1; i++) {
    const current = chatHistory[i];
    const next = chatHistory[i + 1];

    if (
      current.from === "avatar" &&
      current.type === "question" &&
      next.from === "user"
    ) {
      questionAnswerPairs.push({
        questionId: current.questionId, // Include the database question ID
        question: current.message,
        category: current.category || "general",
        answer: next.message,
        score: Math.floor(Math.random() * 3) + 6,
        feedback:
          "Your response shows good understanding. Consider adding more specific examples and details.",
        improvements: [
          "Use more specific examples from your experience",
          "Structure your response using a clear framework",
        ],
        responseTime: next.responseTime,
        answerDuration: next.answerDuration,
        fillerWordCount: next.fillerWordCount,
      });
    }
  }

  const overallScore =
    questionAnswerPairs.length > 0
      ? Math.round(
          questionAnswerPairs.reduce((sum, qa) => sum + qa.score, 0) /
            questionAnswerPairs.length
        )
      : 6;

  const totalFillerWords = userAnswers.reduce(
    (sum, m) => sum + (m.fillerWordCount || 0),
    0
  );
  const totalResponseTime = userAnswers.reduce(
    (sum, m) => sum + (m.responseTime || 0),
    0
  );
  const totalAnswerDuration = userAnswers.reduce(
    (sum, m) => sum + (m.answerDuration || 0),
    0
  );
  const averageResponseTime =
    userAnswers.length > 0 ? totalResponseTime / userAnswers.length : 0;
  const averageAnswerDuration =
    userAnswers.length > 0 ? totalAnswerDuration / userAnswers.length : 0;

  return {
    overallScore,
    oneLineSummary:
      "Interview completed successfully with room for improvement in providing detailed examples.",
    strengths: [
      "Successfully completed the interview process",
      "Demonstrated basic understanding of the questions",
      "Showed engagement and participation throughout",
    ],
    areasForImprovement: [
      "Provide more specific examples from your experience",
      "Improve answer structure and organization",
      "Practice speaking with more confidence",
    ],
    averageResponseTime: parseFloat(averageResponseTime.toFixed(2)),
    averageAnswerDuration: parseFloat(averageAnswerDuration.toFixed(2)),
    totalFillerWords,
    questionAnalysis: questionAnswerPairs,
  };
}
